<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wbgame.mapper.master.DkSecretKeyMapper">
  <resultMap id="BaseResultMap" type="com.wbgame.pojo.DkSecretKey">
    <!--@mbg.generated-->
    <!--@Table dk_secret_key-->
    <id column="dd_version" jdbcType="VARCHAR" property="ddVersion" />
    <id column="sha1" jdbcType="VARCHAR" property="sha1" />
    <result column="secret_key" jdbcType="VARCHAR" property="secretKey" />
    <result column="creater" jdbcType="VARCHAR" property="creater" />
    <result column="appid" jdbcType="VARCHAR" property="appid" />
    <result column="packageName" jdbcType="VARCHAR" property="packageName" />
    <result column="appName" jdbcType="VARCHAR" property="appName" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    dd_version, sha1, secret_key, creater, create_time, update_time
  </sql>
  <select id="selectByPrimaryKey" parameterType="map" resultMap="BaseResultMap">
    <!--@mbg.generated-->
    select
    <include refid="Base_Column_List" />
    from dk_secret_key
    where dd_version = #{ddVersion,jdbcType=VARCHAR}
      and sha1 = #{sha1,jdbcType=VARCHAR}
  </select>
    <select id="selectByPrimaryKeyv2" parameterType="map" resultMap="BaseResultMap">
    <!--@mbg.generated-->
    select
    <include refid="Base_Column_List" />
    from dk_secret_key_v2
    where pkg = #{pkg,jdbcType=VARCHAR}
      and sha1 = #{sha1,jdbcType=VARCHAR}
      and dd_version = #{ddVersion}
  </select>
  
  <delete id="deleteByPrimaryKey" >
    <!--@mbg.generated-->
      <choose>
          <when test=" ddVersion == '2.0' or ddVersion == '3.0'">
              delete from dk_secret_key_v2
              where dd_version = #{ddVersion,jdbcType=VARCHAR}
              and sha1 = #{sha1,jdbcType=VARCHAR}
          </when>
          <otherwise>
              delete from dk_secret_key
              where dd_version = #{ddVersion,jdbcType=VARCHAR}
              and sha1 = #{sha1,jdbcType=VARCHAR}
          </otherwise>
      </choose>

  </delete>

  <insert id="insert" parameterType="com.wbgame.pojo.DkSecretKey">
    <!--@mbg.generated-->
    insert into dk_secret_key (dd_version, sha1, secret_key,
      creater, create_time, update_time
      )
    values (#{ddVersion,jdbcType=VARCHAR}, #{sha1,jdbcType=VARCHAR}, #{secretKey,jdbcType=VARCHAR},
      #{creater,jdbcType=VARCHAR}, #{createTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.wbgame.pojo.DkSecretKey">
    <!--@mbg.generated-->
    insert into dk_secret_key
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="ddVersion != null">
        dd_version,
      </if>
      <if test="sha1 != null">
        sha1,
      </if>
      <if test="secretKey != null">
        secret_key,
      </if>
      <if test="creater != null">
        creater,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="ddVersion != null">
        #{ddVersion,jdbcType=VARCHAR},
      </if>
      <if test="sha1 != null">
        #{sha1,jdbcType=VARCHAR},
      </if>
      <if test="secretKey != null">
        #{secretKey,jdbcType=VARCHAR},
      </if>
      <if test="creater != null">
        #{creater,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
   <insert id="insertSelectivev2" parameterType="com.wbgame.pojo.DkSecretKey">
    <!--@mbg.generated-->
    insert into dk_secret_key_v2
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="ddVersion != null">
        dd_version,
      </if>
      <if test="sha1 != null">
        sha1,
      </if>
      <if test="secretKey != null">
        secret_key,
      </if>
      <if test="creater != null">
        creater,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="appid != null">
        appid,
      </if>
       <if test="appName != null">
        appName,
      </if>
       <if test="packageName != null">
        pkg,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="ddVersion != null">
        #{ddVersion,jdbcType=VARCHAR},
      </if>
      <if test="sha1 != null">
        #{sha1,jdbcType=VARCHAR},
      </if>
      <if test="secretKey != null">
        #{secretKey,jdbcType=VARCHAR},
      </if>
      <if test="creater != null">
        #{creater,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="appid != null">
        #{appid,jdbcType=VARCHAR},
      </if>
      <if test="appName != null">
        #{appName,jdbcType=VARCHAR},
      </if>
       <if test="packageName != null">
        #{packageName,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.wbgame.pojo.DkSecretKey">
    <!--@mbg.generated-->
    update dk_secret_key
    <set>
      <if test="secretKey != null">
        secret_key = #{secretKey,jdbcType=VARCHAR},
      </if>
      <if test="creater != null">
        creater = #{creater,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where dd_version = #{ddVersion,jdbcType=VARCHAR}
      and sha1 = #{sha1,jdbcType=VARCHAR}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.wbgame.pojo.DkSecretKey">
    <!--@mbg.generated-->
    update dk_secret_key
    set secret_key = #{secretKey,jdbcType=VARCHAR},
      creater = #{creater,jdbcType=VARCHAR},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP}
    where dd_version = #{ddVersion,jdbcType=VARCHAR}
      and sha1 = #{sha1,jdbcType=VARCHAR}
  </update>

<!--auto generated by MybatisCodeHelper on 2021-03-01-->
  <select id="selectByAll" resultMap="BaseResultMap">


    <choose>

        <when test="isTest">
            SELECT
            s.*,
            tmp.packageName,
            tmp.appName,
            tmp.appid
            FROM
            dk_secret_key s
            LEFT JOIN (
            SELECT DISTINCT
            T0.packageName,
            T2.gameName appName,
            T3.channel AS channelName,
            T2.appid,
            T1.SHA1
            FROM
            (
            SELECT DISTINCT
            Tf.packageName, IF(Tf.channel IS NULL,'fdb0146a24f68d50',Tf.channel)  channel,
            Tf.typeName
            FROM
            wbgui_formconfig Tf
            ) T0
            LEFT JOIN ( SELECT channelid, gametypeid, SHA1 FROM wbgui_signature UNION SELECT channelid, gametypeid, SHA1 FROM wbgui_signex ) T1 ON T0.typeName = T1.gametypeid
            AND T0.channel = T1.channelid
            LEFT JOIN wbgui_gametype T2 ON T0.typeName = T2.gameId
            LEFT JOIN wbgui_channel T3 ON T0.channel = T3.id
            WHERE
            IFNULL( T1.SHA1, '' ) != '') tmp ON tmp.SHA1 = s.sha1
        </when>
        <otherwise>
            select s.*, tmp.packageName, tmp.appName, tmp.appid from dk_secret_key s left join (
            SELECT DISTINCT
            T0.packageName,
            T2.gameName appName,
            T3.channel AS channelName,
            T2.appid,
            T1.SHA1
            FROM
            (
            SELECT DISTINCT
            Tf.packageName,
            IF(Tf.channel IS NULL,'fdb0146a24f68d50',Tf.channel)  channel,
            Tf.typeName
            FROM
            dnwx_client.wbgui_formconfig Tf
            ) T0
            LEFT JOIN ( SELECT channelid, gametypeid, SHA1 FROM dnwx_client.wbgui_signature UNION SELECT channelid, gametypeid, SHA1 FROM dnwx_client.wbgui_signex ) T1 ON T0.typeName = T1.gametypeid
            AND T0.channel = T1.channelid
            LEFT JOIN dnwx_client.wbgui_gametype T2 ON T0.typeName = T2.gameId
            LEFT JOIN dnwx_client.wbgui_channel T3 ON T0.channel = T3.id
            WHERE
            IFNULL( T1.SHA1, '' ) != ''

            ) tmp on tmp.SHA1 = s.sha1

        </otherwise>
    </choose>

      <where>
          <if test="dkSecretKey.ddVersion != null and dkSecretKey.ddVersion != ''">
              and s.dd_version=#{dkSecretKey.ddVersion,jdbcType=VARCHAR}
          </if>
          <if test="dkSecretKey.sha1 != null and dkSecretKey.sha1 != ''">
              and s.sha1=#{dkSecretKey.sha1,jdbcType=VARCHAR}
          </if>
          <if test="dkSecretKey.secretKey != null">
              and s.secret_key=#{dkSecretKey.secretKey,jdbcType=VARCHAR}
          </if>
          <if test="dkSecretKey.creater != null">
              and s.creater=#{dkSecretKey.creater,jdbcType=VARCHAR}
          </if>
          <if test="dkSecretKey.createTime != null">
              and s.create_time=#{dkSecretKey.createTime,jdbcType=TIMESTAMP}
          </if>
          <if test="dkSecretKey.appid != null and dkSecretKey.appid != ''">
              and tmp.appid in (${dkSecretKey.appid})
          </if>
      </where>
      union all
      select `dd_version` ddVersion,`sha1` ,`secret_key` secretKey, `creater` ,`create_time` createTime,`update_time` updateTime ,`pkg` packageName,`appName` ,`appid`  from dk_secret_key_v2
      <where>
          <if test="dkSecretKey.ddVersion != null and dkSecretKey.ddVersion != ''">
              and dd_version=#{dkSecretKey.ddVersion,jdbcType=VARCHAR}
          </if>
          <if test="dkSecretKey.sha1 != null and dkSecretKey.sha1 != ''">
              and sha1=#{dkSecretKey.sha1,jdbcType=VARCHAR}
          </if>
          <if test="dkSecretKey.secretKey != null">
              and secret_key=#{dkSecretKey.secretKey,jdbcType=VARCHAR}
          </if>
          <if test="dkSecretKey.creater != null">
              and creater=#{dkSecretKey.creater,jdbcType=VARCHAR}
          </if>
          <if test="dkSecretKey.createTime != null">
              and create_time=#{dkSecretKey.createTime,jdbcType=TIMESTAMP}
          </if>
          <if test="dkSecretKey.appid != null and dkSecretKey.appid != ''">
              and (appid in (${dkSecretKey.appid}) or appid is null or appid = '')
          </if>
      </where>
      
    <!--select
        <include refid="Base_Column_List" />
        from dk_secret_key
        <where>
            <if test="ddVersion != null and ddVersion != ''">
                and dd_version=#{ddVersion,jdbcType=VARCHAR}
            </if>
            <if test="sha1 != null and sha1 != ''">
                and sha1=#{sha1,jdbcType=VARCHAR}
            </if>
            <if test="secretKey != null">
                and secret_key=#{secretKey,jdbcType=VARCHAR}
            </if>
            <if test="creater != null">
                and creater=#{creater,jdbcType=VARCHAR}
            </if>
            <if test="createTime != null">
                and create_time=#{createTime,jdbcType=TIMESTAMP}
            </if>
        </where>-->
    </select>


    <select id="selectSha1" resultType="java.lang.String" >

        SELECT DISTINCT SHA1 FROM dnwx_client.wbgui_signature where SHA1 is not null
        UNION
        SELECT DISTINCT SHA1 FROM dnwx_client.wbgui_signex where SHA1 is not null

        <!--select DISTINCT SHA1 from dnwx_client.wbgui_signature where SHA1 is not null-->
    </select>
        <select id="selectAppInfoByPkg" resultType="com.wbgame.pojo.DkSecretKey" parameterType="java.lang.String"  >
        SELECT SUBSTR(pjId ,1,5) as appid,gameName as appName FROM dnwx_client.wbgui_formconfig where packageName =#{pkg}  LIMIT 1
    </select>
    
</mapper>