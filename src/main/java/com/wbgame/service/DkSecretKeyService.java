package com.wbgame.service;

import com.wbgame.pojo.DkSecretKey;

import java.util.List;

public interface DkSecretKeyService {


    int deleteByPrimaryKey(String ddVersion, String sha1);

    int insert(DkSecretKey record);

    int insertSelective(DkSecret<PERSON>ey record);
    
    int insertSelectivev2(DkSecretKey record);
    
    DkSecretKey selectByPrimaryKey(String ddVersion, String sha1);
    DkSecretKey selectByPrimaryKeyv2(String pkg, String sha1, String ddVersion);
    int updateByPrimaryKeySelective(DkSecret<PERSON>ey record);

    int updateByPrimaryKey(DkSecretKey record);

    List<DkSecretKey> selectByAll(DkSecretKey dkSecretKey);
    
    DkSecretKey selectAppInfoByPkg(String pkg);

    List<String> selectSha1();
}


