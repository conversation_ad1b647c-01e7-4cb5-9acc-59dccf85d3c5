package com.wbgame.service.impl;

import com.wbgame.mapper.master.DkSecretKeyMapper;
import com.wbgame.pojo.DkSecretKey;
import com.wbgame.service.DkSecretKeyService;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

@Service
public class DkSecretKeyServiceImpl implements DkSecretKeyService {

    @Resource
    private DkSecretKeyMapper dkSecretKeyMapper;

    @Value("${spring.profiles.active}")
    private String active;

    @Override
    public int deleteByPrimaryKey(String ddVersion, String sha1) {
        return dkSecretKeyMapper.deleteByPrimaryKey(ddVersion, sha1);
    }

    @Override
    public int insert(DkSecretKey record) {
        return dkSecretKeyMapper.insert(record);
    }

    @Override
    public int insertSelective(DkSecretKey record) {
        return dkSecretKeyMapper.insertSelective(record);
    }

    @Override
    public int insertSelectivev2(DkSecretKey record) {
        return dkSecretKeyMapper.insertSelectivev2(record);
    }
    @Override
    public DkSecretKey selectByPrimaryKey(String ddVersion, String sha1) {
        return dkSecretKeyMapper.selectByPrimaryKey(ddVersion,sha1);
    }
    @Override
    public DkSecretKey selectByPrimaryKeyv2(String pkg, String sha1, String ddVersion) {
        return dkSecretKeyMapper.selectByPrimaryKeyv2(pkg,sha1,ddVersion);
    }
    @Override
    public int updateByPrimaryKeySelective(DkSecretKey record) {
        return dkSecretKeyMapper.updateByPrimaryKeySelective(record);
    }
    @Override
    public int updateByPrimaryKey(DkSecretKey record) {
        return dkSecretKeyMapper.updateByPrimaryKey(record);
    }

    @Override
    public List<DkSecretKey> selectByAll(DkSecretKey dkSecretKey) {

        // 判断是否是测试环境 true:测试环境 false:不是
        boolean isTest = "test".equals(active);
        return dkSecretKeyMapper.selectByAll(dkSecretKey, isTest);
    }

    @Override
    public List<String> selectSha1() {
        return dkSecretKeyMapper.selectSha1();
    }

	@Override
	public DkSecretKey selectAppInfoByPkg(String pkg) {
		return dkSecretKeyMapper.selectAppInfoByPkg(pkg);
	}

}


