package com.wbgame.task;

import java.time.LocalDate;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ArrayBlockingQueue;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import com.wbgame.mapper.master.YdMapper;
import com.wbgame.mapper.master.YyhzMapper;
import com.wbgame.pojo.AdServingProductConfigVo;
import com.wbgame.pojo.AppInfoVo;
import com.wbgame.service.*;
import com.wbgame.service.mobile.SysTrackService;
import com.wbgame.utils.BlankUtils;
import com.wbgame.utils.FeishuUtils;
import com.wbgame.utils.MailToolTwo;
import org.joda.time.DateTime;
import org.joda.time.format.DateTimeFormat;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.ListOperations;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import com.wbgame.mapper.slave.CustomMapper;
import com.wbgame.pojo.RedPackConfig;
import com.wbgame.pojo.RespUserInfo;
import com.wbgame.utils.CommonUtil;

import javax.mail.Address;
import javax.mail.internet.AddressException;
import javax.mail.internet.InternetAddress;

@Component
public class SomeTask {
	Logger logger = LoggerFactory.getLogger(SomeTask.class);

	@Autowired
	private RedisTemplate<String, Object> redisTemplate;
	@Autowired
	private SomeService someService; 
	@Autowired
	private YdService ydService;
	@Autowired
	AdServingPageService adServingPageService;
	@Autowired
	PlatformDataService platformDataService;
	@Autowired
	private YyhzMapper yyhzMapper;
	@Autowired
	private ViolationRecordService violationRecordService;
	@Autowired
	private SysTrackService sysTrackService;
	@Autowired
	private OppoViolationRecordService oppoViolationRecordService;

	/* 渠道自动提审告警 */
	public static String CHANNEL_CHAT_ID = "oc_e54b4dd7fa5ebe24237d6b6bde86c307";

	/**
	 * 渠道广告自动提审过审-定点检查
	 */
	@Scheduled(cron="00 00 18 * * ?")
	public void checkWbguiChannelAdsid(){
		try {
			Map<String, String> paramMap = new HashMap<>();
			String day = DateTime.now().toString("yyyy-MM-dd");
			String tdate = DateTime.now().minusDays(60).toString("yyyy-MM-dd");
			paramMap.put("tdate", tdate);

			/* 审核中状态需求单检测 */
			List<Map<String, Object>> list = yyhzMapper.selectWbguiChannelAdsid(paramMap);
			if(list != null && list.size() > 0){
				String temp = (
						"\n渠道自动提审报错巡检告警汇总\n" +
						"日期 %s\n" +
						"产品 %s\n" +
						"子渠道 %s\n" +
						"项目id %s\n" +
						"报错信息：错误原因为【检测到缺少video源配置】，需求单状态为【审核中】，麻烦手动检查操作");
				for (Map<String, Object> act : list) {
					String format = String.format(temp, day,act.get("appname")+"-"+act.get("appid"),act.get("cha_id"),act.get("prjid"));
					FeishuUtils.sendMsgToGroupRobot(CHANNEL_CHAT_ID,format,"all");
				}
			}

			/* 非审核中状态需求单检测 */
			List<Map<String, Object>> list2 = yyhzMapper.selectWbguiChannelAdsidTwo(paramMap);
			if(list2 != null && list2.size() > 0){
				String temp = (
						"\n渠道自动提审报错巡检告警汇总\n" +
						"日期 %s\n" +
						"产品 %s\n" +
						"子渠道 %s\n" +
						"项目id %s\n" +
						"报错信息：错误原因为【检测到仍然有video源配置存在】，需求单状态为【%s】，麻烦手动检查操作");
				for (Map<String, Object> act : list2) {
					String format = String.format(temp, day,act.get("appname")+"-"+act.get("appid"),act.get("cha_id"),act.get("prjid"),act.get("state"));
					FeishuUtils.sendMsgToGroupRobot(CHANNEL_CHAT_ID,format,"all");
				}
			}

			logger.info("完成checkWbguiChannelAdsid同步任务 -- ");
		} catch (Exception e) {
			e.printStackTrace();
			logger.info("checkWbguiChannelAdsid同步任务失败 -- ");
		}
	}


	@Scheduled(cron="10 */5 * * * ?")
	public void syncUserInfoList10086(){
		List<RespUserInfo> dataList = new ArrayList<RespUserInfo>();
		ListOperations<String, Object> opsForList = redisTemplate.opsForList();
		
		int size = opsForList.size(CommonUtil.REDIS_LIST_SOME_USERINFOLOG+"10086").intValue();
		logger.info("some userInfo10086有数据 "+size+"条");
		for (int i = 0; i < size; i++) {
			
			RespUserInfo user = (RespUserInfo)opsForList.rightPop(CommonUtil.REDIS_LIST_SOME_USERINFOLOG+"10086");
			dataList.add(user);
		}
		
		if(dataList != null && dataList.size() > 0){ // 包含数据
			try {
				long start = System.currentTimeMillis();
				Map<String, Object> map = new HashMap<String, Object>();
				map.put("tableName","some_user_info_10086");
				map.put("list", dataList);
				someService.insertUserInfo(map);
				
				map.clear();
				dataList.clear();
				long end = System.currentTimeMillis();
				logger.info("some userInfo10086消耗  "+ (end - start)/1000.00 + " 秒");
			} catch (Exception e) {
	        	e.printStackTrace();
	        }
		}
	}
	
	@Scheduled(cron="10 */5 * * * ?")
	public void syncUserInfoList19068(){
		List<RespUserInfo> dataList = new ArrayList<RespUserInfo>();
		ListOperations<String, Object> opsForList = redisTemplate.opsForList();
		
		int size = opsForList.size(CommonUtil.REDIS_LIST_SOME_USERINFOLOG+"19068").intValue();
		logger.info("some userInfo19068有数据 "+size+"条");
		for (int i = 0; i < size; i++) {
			RespUserInfo user = (RespUserInfo)opsForList.rightPop(CommonUtil.REDIS_LIST_SOME_USERINFOLOG+"19068");
			dataList.add(user);
		}
		
		if(dataList != null && dataList.size() > 0){ // 包含数据
			try {
				long start = System.currentTimeMillis();
				Map<String, Object> map = new HashMap<String, Object>();
				map.put("tableName","some_user_info_19068");
				map.put("list", dataList);
				someService.insertUserInfo(map);
				
				map.clear();
				dataList.clear();
				long end = System.currentTimeMillis();
				logger.info("some userInfo19068消耗  "+ (end - start)/1000.00 + " 秒");
			} catch (Exception e) {
				e.printStackTrace();
			}
		}
	}


	/**
	 * 每周日删除设置为关闭状态的资源
	 */
	@Scheduled(cron = "0 0 5 ? * 1")
	public void delAdServingPage(){
		DateTime now = DateTime.now();
		logger.info("delAdServingPage start");
		try {
			AdServingProductConfigVo vo = new AdServingProductConfigVo();
			List<AdServingProductConfigVo> list= adServingPageService.selectAdServingProductList(vo);
			list = list.stream().filter(t->"0".equals(t.getStatu())&& !BlankUtils.checkBlank(t.getEndtime())).collect(Collectors.toList());
			StringBuffer sb = new StringBuffer();
			list.forEach(t->{
				DateTime endTime = DateTime.parse(t.getEndtime(), DateTimeFormat.forPattern("yyyy-MM-dd HH:mm:ss.0"));
				if (endTime.plusDays(14).isBefore(now)){
					sb.append(t.getId()+",");
				}
			});
			if (sb.length()>0){
				String delIds = sb.toString().substring(0,sb.length()-1);
				vo.setId(delIds);
				//删除表数据
				adServingPageService.delAdServingProduct(vo);
				//刷新缓存
				adServingPageService.invalidateAdServingProductMap();
			}
		}catch (Exception e){
			logger.error("delAdServingPage error:",e);
		}
		logger.info("delAdServingPage end");
	}

	ThreadPoolExecutor platformDataPool = new ThreadPoolExecutor(6, 6, 0L, TimeUnit.SECONDS,
			new ArrayBlockingQueue<>(1000),
			r -> new Thread(r, "platform-task-"),
			new ThreadPoolExecutor.CallerRunsPolicy());

	/**
	 * 每天9/16:50拉取渠道详情页转化分析
	 */
	@Scheduled(cron = "0 30 9,14,16 * * ?")
	public void syncPlatformTask(){
		DateTime now = DateTime.now();
		String[] platforms = {"oppo","xiaomi","vivo","huawei"};
		String startTime = now.minusDays(8).toString("yyyy-MM-dd");
		String endTime = now.minusDays(1).toString("yyyy-MM-dd");

		for (String plarform : platforms) {
			//详情页分发分析
			try {
				logger.info("syncPlatformTask page data start platform:" + plarform);
				platformDataPool.execute(() -> platformDataService.syncPlatformDetailPageData(startTime, endTime, plarform,""));
				String before_31_date = now.minusDays(31).toString("yyyy-MM-dd");
				platformDataPool.execute(() ->platformDataService.syncPlatformDetailPageData(before_31_date, before_31_date, plarform,""));
				logger.info("syncPlatformTask page data end platform:" + plarform);
			} catch (Exception e) {
				logger.error("syncPlatformTask page data error platform:", e);
				continue;
			}
		}
		//单独更新oppo渠道14日留存 其他平台没有14日留存字段
		platformDataPool.execute( () ->platformDataService.syncPlatformDetailPageData(now.minusDays(15).toString("yyyy-MM-dd"), now.minusDays(15).toString("yyyy-MM-dd"), "oppo",""));
		String[] platform2s = {"oppo","xiaomi"};
		for (String plarform : platform2s) {
			try {
				logger.info("syncPlatformTask grade data start platform:" + plarform);
				platformDataService.syncPlatformGradeData(plarform);
				logger.info("syncPlatformTask grade data end platform:" + plarform);
			} catch (Exception e) {
				logger.error("syncPlatformTask grade data error platform:", e);
				continue;
			}
		}

		String[] platform3s = {"huawei","xiaomi","vivo","oppo"};
		for (String plarform : platform3s) {
			try {
				logger.info("syncPlatformReservationTask grade data start platform:" + plarform);
				platformDataService.syncPlatformReservationData(startTime, endTime, plarform,"");
				logger.info("syncPlatformReservationTask data end platform:" + plarform);
			} catch (Exception e) {
				logger.error("syncPlatformReservationTask data error platform:", e);
				continue;
			}
		}
	}


	/**
	 * 8点55（无需告警）、19点55（无需告警）、10点55~17点55（需要告警）每小时同步一次触发拉取 60天内小米违规记录数据
	 */
	@Scheduled(cron = "0 55 8,10-17,19,20,21 * * ?")
	public void syncViolationRecordTask(){
		DateTime now = DateTime.now();
		String startTime = now.minusDays(60).toString("yyyy-MM-dd");
		String endTime = now.toString("yyyy-MM-dd");
		int currentHour = now.getHourOfDay();
		if (currentHour >= 10 && currentHour <= 18) {
			violationRecordService.pullViolationRecords(startTime,endTime,null,true,true);
		} else {
			violationRecordService.pullViolationRecords(startTime,endTime,null,false,true);
		}
	}

	/**
	 * 小米违规记录数据 拉取活跃模块数据
	 */
	@Scheduled(cron = "0 30 8,12,20 * * ?")
	public void pullViolationTransformFields(){
		DateTime now = DateTime.now();
		String startTime = now.minusDays(3).toString("yyyy-MM-dd");
		String endTime = now.toString("yyyy-MM-dd");
		violationRecordService.pullTransformFields(startTime,endTime);
	}


	/**
	 * 拉取oppo违规记录数据: 最近三天
	 */
	@Scheduled(cron = "0 30 8,12,16 * * ?")
	public void pullOppoViolations(){
		oppoViolationRecordService.fetchEmails(2,"<EMAIL>",null);
	}

	/**
	 * 6\13\21点30分同步昨天的小米填充率诊断
	 */
	@Scheduled(cron = "0 30 6,13,21 * * ?")
	public void syncXiaomiFillingRateTask(){
		LocalDate localDate = LocalDate.now().minusDays(1);
		String date = localDate.toString();
		violationRecordService.pullPlacementErrorDetail(date, date, null);
	}

	/**
	 * 系统埋点缓存数据刷新至数据库：每隔一小时触发一次
	 */
	@Scheduled(cron = "0 30 * * * ?")
	public void pullRedisEvents(){
		sysTrackService.pullRedisEvents();
	}


}
