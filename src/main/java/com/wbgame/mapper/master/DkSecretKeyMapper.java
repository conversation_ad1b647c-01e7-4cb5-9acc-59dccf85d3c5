package com.wbgame.mapper.master;

import com.wbgame.pojo.DkSecretKey;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface DkSecretKeyMapper {
    int deleteByPrimaryKey(@Param("ddVersion") String ddVersion, @Param("sha1") String sha1);

    int insert(DkSecretKey record);

    int insertSelective(DkSecretKey record);
    int insertSelectivev2(DkSecretKey record);
    DkSecretKey selectByPrimaryKey(@Param("ddVersion") String ddVersion, @Param("sha1") String sha1);
    DkSecretKey selectByPrimaryKeyv2(@Param("pkg") String pkg, @Param("sha1") String sha1, @Param("ddVersion") String ddVersion);
    int updateByPrimaryKeySelective(DkSecretKey record);
    int updateByPrimaryKeySelectivev2(DkSecretKey record);
    int updateByPrimaryKey(DkSecretKey record);

    List<DkSecretKey> selectByAll(@Param("dkSecretKey") DkSecretKey dkSecretKey, @Param("isTest") boolean isTest);

    List<String> selectSha1();
    
    DkSecretKey selectAppInfoByPkg(@Param("pkg")String pkg);
}