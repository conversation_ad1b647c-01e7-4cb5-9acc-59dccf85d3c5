package com.wbgame;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.alipay.api.domain.ComplaintInfoQueryResponse;
import com.vi.daemon.key.DkGenarator;
import com.wbgame.common.ReturnJson;
import com.wbgame.controller.advert.query.transport.UmengAdvIncomeController;
import com.wbgame.mapper.adb.*;
import com.wbgame.mapper.adv2.Adv2Mapper;
import com.wbgame.mapper.haiwai2adb.Haiwai2DnwxBiUsMapper;
import com.wbgame.mapper.haiwaiad.HaiwaiCfgMapper;
import com.wbgame.mapper.master.*;
import com.wbgame.mapper.master.finance.master.FinanceAdMapper;
import com.wbgame.mapper.master.jettison.MaterialPathMapper;
import com.wbgame.mapper.redpack.AdvertRollUrlConfigMapper;
import com.wbgame.mapper.redpack.InGamePaymentConfigMapper;
import com.wbgame.mapper.redpack.RedpackAdMapper;
import com.wbgame.mapper.slave2.AdMsgMapper;
import com.wbgame.mapper.slave2.wz.WzRewardConfigMapper;
import com.wbgame.mapper.tfxt.AdtMapper;
import com.wbgame.mapper.tfxt.TfxtMapper;
import com.wbgame.pojo.*;
import com.wbgame.pojo.adv2.DnChaRevenueTotal;
import com.wbgame.pojo.adv2.ExtendAdposVo;
import com.wbgame.pojo.adv2.resultEntity.SyncResult;
import com.wbgame.pojo.adv2.wz.HomeWzTiwaiConfig;
import com.wbgame.pojo.advert.MicGameIncomeVo;
import com.wbgame.pojo.advert.UmengAdIncomeReportVo;
import com.wbgame.pojo.jettison.vo.MaterialPathVo;
import com.wbgame.pojo.operate.InGamePaymentConfigVo;
import com.wbgame.pojo.operate.InGamePaymentReportVo;
import com.wbgame.pojo.pay.AliRefundParam;
import com.wbgame.pojo.product.DnwxX3X4ConfigManageV2Dto;
import com.wbgame.service.*;
import com.wbgame.service.adv2.Adv2Service;
import com.wbgame.service.adv2.impl.Adv2ServiceImpl;
import com.wbgame.service.advert.MicGameIncomeService;
import com.wbgame.service.finance.FinMicGameRevenueService;
import com.wbgame.service.finance.FinanceAdService;
import com.wbgame.service.finance.FinanceService;
import com.wbgame.service.finance.MediumFreeService;
import com.wbgame.service.finance.impl.FinanceAdServiceImpl;
import com.wbgame.service.impl.PlatformDataServiceImpl;
import com.wbgame.service.impl.UmengUserDetailServiceImpl;
import com.wbgame.service.impl.WxGameAppTypeManageServiceImpl;
import com.wbgame.service.impl.platform.xiaomi.XiaomiDataServiceImpl;
import com.wbgame.service.mobile.ApiTransferKeyConfigService;
import com.wbgame.service.syncdata.CashSyncDataService;
import com.wbgame.service.syncdata.impl.DouyinXyxCashSpendService;
import com.wbgame.service.syncdata.sink.ReportService;
import com.wbgame.task.AdChannelTask;
import com.wbgame.task.AddActiveSummaryTask;
import com.wbgame.task.HaiwaiAdTask;
import com.wbgame.task.UsertagTask;
import com.wbgame.utils.*;
import com.wbgame.utils.jettison.StringUtil;
import com.wbgame.utils.wx.WechatPayHttpClientBuilder;
import com.wbgame.utils.wx.auth.AutoUpdateCertificatesVerifier;
import com.wbgame.utils.wx.auth.PrivateKeySigner;
import com.wbgame.utils.wx.auth.WechatPay2Credentials;
import com.wbgame.utils.wx.auth.WechatPay2Validator;
import com.wbgame.utils.wx.util.PemUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.binary.Base64;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.util.EntityUtils;
import org.joda.time.DateTime;
import org.joda.time.Days;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.boot.CommandLineRunner;
import org.springframework.core.annotation.Order;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.io.ByteArrayInputStream;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.nio.file.StandardOpenOption;
import java.security.PrivateKey;
import java.util.*;
import java.util.function.Function;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

@Slf4j
@Component
@Order(0) // 数值越小越先执行
public class MyCommandLineRunner implements CommandLineRunner {

	Logger logger = LoggerFactory.getLogger(MyCommandLineRunner.class);
	public static String accessKey = "1617679647909641842";
	public static String accessSecret = "2b4ef9bc48e0b45cf66a71a4b331b868dc59f88b4274472473e0516868ca73df";

	public static String haiwaiFilter = "(appid in (select id from app_info where app_category in (15,16,20,52)) or cha_id='appleOversea' or cha_id='google_huawei')";

	private final static String AES_KEY="x3aesaabb1230000";

	@Autowired
	@Qualifier("redisRedPacktTemplate")
	private RedisTemplate<String, Object> redisRedPacktTemplate;


	@Autowired
	private WorldService worldService;
	@Autowired
	private WbSysService wbSysService;
	@Autowired
	private AdService adService;
	@Autowired
	private UmengUserDetailService umengUserDetailService;

	@Autowired
	private BigdataService bigdataService;
	@Autowired
	YyhzMapper yyhzMapper;
	@Autowired
	HaiwaiCfgMapper haiwaiCfgMapper;
	@Autowired
	AdMapper adMapper;
	@Autowired
	Adv2Mapper adv2Mapper;
	@Autowired
	private FinanceAdMapper financeAdMapper;

	@Autowired
	private WxGameAppTypeManageService wxGameAppTypeManageService;
	@Autowired
	private PartnerService partnerService;
	@Autowired
	private DnwxBiMapper dnwxBiMapper;
	@Autowired
	private LeyuanService leyuanService;


	@Autowired
	InGamePaymentConfigMapper inGamePaymentConfigMapper;
	@Autowired
	private PlatformDataService platformDataService;
	@Autowired
	private AddActiveSummaryTask addActiveSummaryTask;

	@Override
	public void run(String... args) throws Exception {
		System.out.println("初始化缓存数据...");

		CommonUtil.cMap = worldService.selectWxAppConfigMap();
		CommonUtil.gMap = worldService.selectWxIconGnameAll();
		CommonUtil.fdsMap = worldService.selectWxFdsConfig();
		CommonUtil.bonusMap = worldService.selectWxBonusConfig();
		CommonUtil.helpMap = worldService.selectWxHelpConfig();
		CommonUtil.switchMap = worldService.selectWxPowerSwitch();
		CommonUtil.pageSwitchMap = worldService.selectWxPageSwitch();
		CommonUtil.templateMap = worldService.selectWxCustomTemplate();
		CommonUtil.redpackMap = worldService.selectWxRedPackConfig();
		CommonUtil.mpPayMap = worldService.selectWxMpPayConfig();
		CommonUtil.msgList = worldService.selectWxMoneyMsgList();

		CommonUtil.userMap = wbSysService.selectWbSysUserAllMap();
		CommonUtil.appGroupMap = wbSysService.selectWbSysAppGroupMap();

		CommonUtil.waibaoUserMap = wbSysService.selectWaibaoUserAllMap();


		for (int i = 0; i < 31; i++) {
//			String day = DateTime.parse("2025-03-01").plusDays(i).toString("yyyy-MM-dd");
//			System.out.println(day + " 执行...");
//			adService.syncDnAppRevenueTotal(day, "40465");


//			String sql2 = "SELECT `day` tdate,app appid,TRUNCATE(SUM(rebateSpend),2) rebate_consume FROM dnwx_adt.dn_report_spend_china where `day` = '"+day+"' and app in (40691,40762,40430,40421) GROUP BY app";
//			System.out.println("sql2=="+sql2);
//			List<Map<String, Object>> listMap2 = dnwxBiAdtMapper.queryListMap(sql2);
//			try {
//				Map<String, Object> paramMap2 = new HashMap<String, Object>();
//				paramMap2.put("sql1", "update dn_app_revenue_total set rebate_consume=#{li.rebate_consume} where tdate=#{li.tdate} and appid=#{li.appid} ");
//				paramMap2.put("list", listMap2);
//				adMapper.batchExecSqlTwo(paramMap2);
//			} catch (Exception e) {
//				e.printStackTrace();
//			}

		}





		// 使用循环日期，获取1号到28号的数据
		for (int i = 0; i < 4; i++) {
//			String date = DateTime.parse("2025-06-25").plusDays(i).toString("yyyy-MM-dd");
//			System.out.println(date + " 执行...");
//			addActiveSummaryTask.summaryTask(date);

			try {
				String start_date = "2025-06-01";
				String end_date = "2025-06-23";

				Map<String, String> paramMap = new HashMap<>();
				paramMap.put("start_date", start_date);
				paramMap.put("end_date", end_date);

				// 17-微信-IAP，39-微信-IAA，43-抖音小游戏，47-快手小游戏
				String check = "select id from app_info where app_category in (45) ";
				List<String> appList = yyhzMapper.queryListString(check);
				String apps = String.join(",", appList);

				// 增加 45-华为快游戏-游戏
				String query2 = "SELECT tdate,appid,CONCAT('h5_',platform) cha_id,addnum add_num,dau act_num,avg_duration FROM adv_platform_pagedata_info where tdate BETWEEN #{obj.start_date} AND #{obj.end_date} and platform='huawei' " +
						"and appid in ( select id from app_info where app_category in (45) )";
				List<Map<String, Object>> forList = yyhzMapper.queryListMapTwo(query2, paramMap);

				/** 执行更新操作，写入新增活跃到 dn_adt.dn_report_monetization_summary_china 变现汇总表 */
				try {

					Map<String, Object> paramMap2 = new HashMap<String, Object>();
					paramMap2.put("sql1", "update dn_report_monetization_summary_china set dau=#{li.act_num},installs=#{li.add_num},duration=#{li.avg_duration} where `day`=#{li.tdate} and app=#{li.appid} and cha_id=#{li.cha_id} and app in ("+apps+") ");
					paramMap2.put("list", forList);
					// 2023.8.1 切换至变现表
					adv2Mapper.batchExecSqlTwo(paramMap2);

				} catch (Exception e) {
					e.printStackTrace();
				}

			} catch (Exception e) {
				e.printStackTrace();
				logger.info("变现汇总表新增活跃同步 异常...");
			}

//			try {
//				String sql = "delete from dnwx_cfg.dn_extend_revenue_report where date = '"+date+"'";
//				adService.execSql(sql);
//
//				adMapper.insertExtendRevenueReport(date);
//
//				List<Map<String, String>> dataList = haiwaiCfgMapper.selectExtendRevenueReport(date);
//				if(dataList != null && !dataList.isEmpty()) {
//					// 使用adMapper进行批量写入
//					Map<String, Object> paramMap = new HashMap<String, Object>();
//					paramMap.put("sql1", "INSERT INTO dnwx_cfg.dn_extend_revenue_report(date,dnappid,agent,placement_type,request_count,return_count,pv,revenue) values ");
//					paramMap.put("sql2", " (#{li.date},#{li.dnappid},#{li.agent},#{li.placement_type},#{li.request_count},#{li.return_count},#{li.pv},#{li.revenue}) ");
//					paramMap.put("sql3", " ON DUPLICATE KEY UPDATE request_count=VALUES(request_count),return_count=VALUES(return_count),pv=VALUES(pv),revenue=VALUES(revenue)");
//					paramMap.put("list", dataList);
//					adMapper.batchExecSql(paramMap);
//
//					logger.info("海外数据批量写入变现数据汇总报表完成，共处理 " + dataList.size() + " 条记录");
//				} else {
//					logger.info("海外数据批量写入变现数据汇总报表完成，无数据需要处理");
//				}
//
//				logger.info("同步syncRevenueReport 完成...");
//			} catch (Exception e) {
//				e.printStackTrace();
//				logger.info("同步syncRevenueReport 异常...");
//			}


//
//			partnerService.syncPartnerAppBillingNewOverseas(date, null);
//
//			// 将投放和收入数据，写入到合作方产品收支 umeng_channel_cost
//			Map<String, String> paramMap = new HashMap<String, String>();
//			paramMap.put("sdate", date);
//			paramMap.put("edate", date);
//			partnerService.checkPartnerAppCostTotal(paramMap);
		}



		/** 转发给ADB数据库 */
//		logger.info("syncAdsDnShowGapAutoctr同步开始...");
//		// 自统计点击率 实际数值=（前七天自统计CTR+前一天自统计CTR）/2，点击率gap=前七天平台CTR/前七天自统计CTR（注意不要-1）
//		Map<String, String> paramMap = new HashMap<String, String>();
//		paramMap.put("sdate", DateTime.now().minusDays(8).toString("yyyy-MM-dd"));
//		paramMap.put("edate", DateTime.now().minusDays(2).toString("yyyy-MM-dd"));
//		paramMap.put("yesterday", DateTime.now().minusDays(1).toString("yyyy-MM-dd"));
//		dnwxBiMapper.insertAdsDnShowGapAutoctrAsOut(paramMap);
//
//		logger.info("syncAdsDnShowGapAutoctr同步完成...");


//		for (int i = 0; i < 1000; i++) {
//
//			logger.info("执行syncHaiwaiAdconfig 开始...");
//			String before = DateTime.now().minusMinutes(20).toString("yyyy-MM-dd HH:mm:ss");
//
//			boolean flag = true;
//			/** 广告配置 */
//			try {
//				String query = "select * from dnwx_cfg.dn_extend_adconfig where "+haiwaiFilter;
//
//				List<Map<String, String>> list = yyhzMapper.queryListMapOne(query);
//
//				if(list != null && list.size() > 0) {
//
//					String del = "delete from dnwx_cfg.dn_extend_adconfig where 1=1";
//					haiwaiCfgMapper.execSql(del);
//
//
//					Map<String, Object> paramMap3 = new HashMap<String, Object>();
//					paramMap3.put("sql1", "replace into dnwx_cfg.dn_extend_adconfig(id,appid,cha_id,prjid,buy_id,buy_act,is_newuser,user_group,adpos_type,strategy,adsid,statu,ecpm,priority,rate,createtime,lasttime,cuser) values ");
//					paramMap3.put("sql2", " (#{li.id},#{li.appid},#{li.cha_id},#{li.prjid},#{li.buy_id},#{li.buy_act},#{li.is_newuser},#{li.user_group},#{li.adpos_type},#{li.strategy},#{li.adsid},#{li.statu},#{li.ecpm},#{li.priority},#{li.rate},#{li.createtime},#{li.lasttime},#{li.cuser}) ");
//					paramMap3.put("sql3", " ");
//					paramMap3.put("list", list);
//					haiwaiCfgMapper.batchExecSql(paramMap3);
//
//					logger.info("执行syncHaiwaiAdconfig1 " +list.size()+ "条");
//				}
//
//			} catch (Exception e) {
//				flag = false;
//				e.printStackTrace();
//				logger.info("执行syncHaiwaiAdconfig1 异常...");
//			}
//
//			if(flag) {
//				try {
//					String url = "https://edc.vigame.cn:6115/recacheHaiwaiWjy?mapid=300";
//
//					HttpClientUtils.getInstance().httpGet(url);
//					logger.info("执行syncHaiwaiAdconfig 刷新缓存...");
//				} catch (Exception e) {
//					e.printStackTrace();
//					logger.info("执行syncHaiwaiAdconfig 刷新缓存异常...");
//				}
//			}else{
//				// 飞书通知
//				String msg = "syncHaiwaiAdconfig同步异常，请及时检查！";
//				HaiwaiAdTask.sendFailMsg(msg);
//			}
//
//			Thread.sleep(20*1000);
//		}



//		同步233乐园的数据
//		leyuanService.synLeyuanData("2025-05-01","2025-05-31");




//		platformDataService.syncPlatformAppInfoData("xiaomi","13750000022");

//		platformDataService.syncPlatformAppInfoData("honor","<EMAIL>");



/*
		// 检查不在redis的key列表
		List<String> keys = new ArrayList<>();

		String query = "SELECT * FROM `dn_extend_x_config_v2` ORDER BY update_time desc,create_time desc ";
		List<Map<String, String>> list = adv2Mapper.queryListMapOne(query);
		System.out.println("list=="+list.size());
		for (Map<String, String> act : list) {
			if (!BlankUtils.checkBlank(act.get("prjid"))) {
				String val = (String) redisRedPacktTemplate.opsForValue().get("x3data_" + act.get("prjid"));
				if (val == null) {
					System.out.println("无效key:" + act.get("prjid"));
					keys.add(act.get("prjid"));
				}
			} else {
				if (StringUtil.is_nullString(act.get("appid"))) {
					//项目ID为空时     x3data_渠道
					String val = (String) redisRedPacktTemplate.opsForValue().get("x3data_" + act.get("channel"));
					if (val == null) {
						System.out.println("无效key:" + act.get("channel"));
						keys.add(act.get("channel"));
					}
				} else {
					String val = (String) redisRedPacktTemplate.opsForValue().get("x3data_" + act.get("appid") + act.get("channel"));
					if (val == null) {
						System.out.println("无效key:" + act.get("appid") + act.get("channel"));
						keys.add(act.get("appid") + act.get("channel"));
					}
				}
			}
		}
		System.out.println("无效需处理："+JSON.toJSONString(keys));
*/

//		String day = DateTime.now().minusDays(1).toString("yyyy-MM-dd");
//		bigdataService.syncGroupCom(day);

//		String today = DateTime.now().toString("yyyy-MM-dd");
//		adv2Service.syncRedLineCtrAsConfig("2025-06-05", "vivo");

//		List<OppoViolationRecord> recordList = oppoViolationRecordService.fetchEmailsOfPrompts(1, "<EMAIL>", "验证码".split(","), null);




		System.out.println("测试dk秘钥生成成功...");
		System.out.println("初始化缓存成功...");
	}

	/**
	 * 查询投诉单列表
	 * @return
	 */
	public String wxRefund(String date) throws Exception{
		//获取商户参数
//		List<JSONObject> machList = payRefundMapper.selectMachConfigAll(null);
//		if (machList == null || machList.isEmpty()) {
//			return ReturnJson.toErrorJson("未找到商户配置");
//		}
		// 查询微信内购参数配置
		InGamePaymentReportVo param = new InGamePaymentReportVo();
		param.setChannel("'wx'");
		List<InGamePaymentConfigVo> collect = inGamePaymentConfigMapper.selectList(param);
		if (collect == null || collect.size() == 0) {
			return ReturnJson.toErrorJson("未找到微信内购参数配置,请先配置参数再查询订单");
		}
		List<String> mchArray = Arrays.asList(
//				"1573970871", // 深圳市动能无线传媒有限公司-发射
				"1609950581", // 深圳市统掌科技有限公司-用心娱乐
				"1623934073"); // 深圳市统掌科技有限公司-全心娱乐
		// 根据Machid去重，然后转为JSONObject列表
		List<JSONObject> machList = collect.stream()
				.filter(act -> mchArray.contains(act.getMachid()))
				.collect(Collectors.groupingBy(InGamePaymentConfigVo::getMachid))
				.values().stream()
				.map(list -> {
					InGamePaymentConfigVo first = list.get(0);
					JSONObject jsonObject = new JSONObject();
					jsonObject.put("mach_id", first.getMachid());
					jsonObject.put("private_key", first.getPrivate_key());
					jsonObject.put("mch_no", first.getMch_serial());
					jsonObject.put("api_key", first.getApi_key());
					return jsonObject;
				}).collect(Collectors.toList());
		System.out.println("商户配置: " + JSON.toJSONString(machList));

		for (JSONObject mach : machList) {

			String mchId = mach.getString("mach_id");
			String privateKey = mach.getString("private_key");
			String mchSerialNo = mach.getString("mch_no");
			String apiV3Key = mach.getString("api_key");

			CloseableHttpClient httpClient = null;
			try {
				// 加载商户私钥（privateKey：私钥字符串）
				PrivateKey merchantPrivateKey = PemUtil
						.loadPrivateKey(new ByteArrayInputStream(privateKey.getBytes("utf-8")));

				// 加载平台证书（mchId：商户号,mchSerialNo：商户证书序列号,apiV3Key：V3密钥）
				AutoUpdateCertificatesVerifier verifier = new AutoUpdateCertificatesVerifier(
						new WechatPay2Credentials(mchId, new PrivateKeySigner(mchSerialNo, merchantPrivateKey)), apiV3Key.getBytes("utf-8"));

				// 初始化httpClient
				httpClient = WechatPayHttpClientBuilder.create()
						.withMerchant(mchId, mchSerialNo, merchantPrivateKey)
						.withValidator(new WechatPay2Validator(verifier)).build();
			}catch (Exception e) {
				e.printStackTrace();
				log.error("mchId=="+mchId+",加载商户私钥或平台证书失败: " + e.getMessage());
				continue;
			}

			JSONObject reqdata = new JSONObject();
			reqdata.put("complainted_mchid", ""); //商户订单号
			reqdata.put("begin_date", date); //起始日期
			reqdata.put("end_date", date); //结束日期
			// 转为key=val的字符串
			StringBuilder queryString = new StringBuilder();
			reqdata.forEach((key, value) -> {
				if (queryString.length() > 0) {
					queryString.append("&");
				}
				queryString.append(key).append("=").append(value);
			});
			log.info("mchId=="+mchId+",请求参数: " + queryString.toString());


			List<JSONObject> complaintList = new ArrayList<>();
			int complaint_count = 0;

			//请求URL
			HttpGet httpGet = new HttpGet("https://api.mch.weixin.qq.com/v3/merchant-service/complaints-v2?"+queryString.toString());
			httpGet.setHeader("Accept", "application/json");
			//完成签名并执行请求
			CloseableHttpResponse response = httpClient.execute(httpGet);
			try {
				int statusCode = response.getStatusLine().getStatusCode();
				String result = EntityUtils.toString(response.getEntity());
				log.info("投诉单列表:code={},result={}",statusCode,result);
				if (statusCode == 200) {
					if(BlankUtils.isJSONObject(result)) {
						JSONObject jsonObject = JSONObject.parseObject(result);
						complaint_count = jsonObject.getInteger("total_count");
					}
				}else {
					return ReturnJson.toErrorJson("解析出现异常:" + EntityUtils.toString(response.getEntity()));
				}

				JSONObject info = new JSONObject();
				info.put("alipay_account_id", mchId);
				info.put("complaint_count", complaint_count);
				info.put("tdate", date);
				complaintList.add(info);
				// 写入数据
				financeAdMapper.insertAlipayComplaintInfo(info);

				// 从支付订单表拉取订单量
				String query = String.format("select '%s' tdate,param2 alipay_account_id,COUNT(1) order_count from wb_pay_info " +
						"where createtime BETWEEN '%s 00:00:00' and '%s 23:59:59' and orderstatus='success' and paytype='微信支付' group by param2",date,date,date);
				List<Map<String, String>> orderList = adMapper.queryListMapOne(query);
				if(orderList == null || orderList.isEmpty()) {
					log.info("mchId=="+mchId+",未查询到订单数据");
				}else{
					financeAdMapper.updateAlipayComplaintInfoAsOrderCount(orderList);
				}
				log.info("投诉单列表查询完成,共{}条", complaintList.size());
				// 返回结果

			} catch (Exception e) {
				e.printStackTrace();
				log.error("查询异常: " + e.getMessage());
				return ReturnJson.toErrorJson("查询异常: " + e.getMessage());
			} finally {
				//关闭连接
				response.close();
				httpClient.close();
			}
		}
		return ReturnJson.success("查询成功");
	}



}