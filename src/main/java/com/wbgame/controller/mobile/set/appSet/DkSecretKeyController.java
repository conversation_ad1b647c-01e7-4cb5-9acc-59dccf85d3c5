package com.wbgame.controller.mobile.set.appSet;

import com.alibaba.fastjson.JSONObject;
import com.daemon.tools.ddk.DDK;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.vi.daemon.key.DkGenarator;
import com.vi.daemon.key.DkUtils;
import com.wbgame.aop.LoginCheck;
import com.wbgame.common.Constants;
import com.wbgame.common.ReturnJson;
import com.wbgame.pojo.CurrUserVo;
import com.wbgame.pojo.DkSecretKey;
import com.wbgame.service.DkSecretKeyService;
import com.wbgame.utils.BlankUtils;
import com.wbgame.utils.ExportExcelUtil;
import org.joda.time.DateTime;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.CrossOrigin;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.Date;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

/**
 * @description: dk秘钥配置
 * @author: huangmb
 * @date: 2021/03/01
 **/
@RestController
@CrossOrigin
@RequestMapping(value = "/mobile/dkSecretKey")
public class DkSecretKeyController {

    @Autowired
    private DkSecretKeyService dkSecretKeyService;

    /**
     * 查询
     *
     * @param request
     * @param response
     * @return
     */
    @RequestMapping(value = "/list")
    public String list(DkSecretKey dkSecretKey,HttpServletRequest request, HttpServletResponse response) {

        String start = BlankUtils.checkNull(request, "start");
        String limit = BlankUtils.checkNull(request, "limit");
        int pageStart = "".equals(start) == true ? 0 : Integer.parseInt(start);
        int pageSize = "".equals(limit) == true ? 100 : Integer.parseInt(limit);
        // 当前页
        int pageNo = (pageStart / pageSize) + 1;
        PageHelper.startPage(pageNo, pageSize); // 进行分页
        List<DkSecretKey> list = dkSecretKeyService.selectByAll(dkSecretKey);
        return ReturnJson.success(new PageInfo(list));

    }

    /**
     * 导出
     *
     * @param request
     * @param response
     * @return
     */
    @RequestMapping(value = "/export")
    public void export(DkSecretKey dkSecretKey,HttpServletRequest request, HttpServletResponse response) {


        List<DkSecretKey> list = dkSecretKeyService.selectByAll(dkSecretKey);
        Map<String,String> head = new LinkedHashMap();
        head.put("ddVersion","dd版本");
        head.put("sha1","sha1");
        head.put("secretKey","秘钥");
        head.put("creater","修改人");
        head.put("packageName","包名");
        head.put("appName","应用名");
        head.put("appid","应用id");
        head.put("createTime","创建时间");
        head.put("updateTime","修改时间");
        String export_file_name = request.getParameter("export_file_name");
        if (BlankUtils.checkBlank(export_file_name)){
            export_file_name = "DK密钥配置";
        }
        ExportExcelUtil.exportXLSX2(response,list,head,export_file_name+"_" + DateTime.now().toString("yyyyMMdd")+ ".xlsx");

    }

    /**
     * 新增
     *
     * @param request
     * @param response
     * @return
     */
    @RequestMapping(value = "/add")
    public String add(DkSecretKey dkSecretKey,HttpServletRequest request, HttpServletResponse response) {

        if (BlankUtils.checkBlank(dkSecretKey.getDdVersion()) || BlankUtils.checkBlank(dkSecretKey.getSha1())) {
            return ReturnJson.error(Constants.ParamError);
        }
        CurrUserVo user = (CurrUserVo)request.getAttribute("LoginUser");
        if(user == null){
        	dkSecretKey.setCreater(dkSecretKey.getCreater());
        }else{
        	dkSecretKey.setCreater(user.getLogin_name());
        }
        String key = "";
        //计算秘钥
        if("3.0".equals(dkSecretKey.getDdVersion())) {
            if (BlankUtils.checkBlank(dkSecretKey.getPackageName())) {
                return ReturnJson.error(Constants.ParamError);
            }
            key = com.daemon.tools.ddk3.DDK.genDDK(dkSecretKey.getPackageName(), dkSecretKey.getSha1()); // 使用v3版本
            dkSecretKey.setSecretKey(key);
            //通过包名查找appid及appName
            DkSecretKey appInfo = dkSecretKeyService.selectAppInfoByPkg(dkSecretKey.getPackageName());
            //应用信息不存在进行提示返回
            if (null == appInfo) {
                return ReturnJson.toErrorJson("未查找到相应的应用信息");
            }
            //该秘钥是否存在
            if (dkSecretKeyService.selectByPrimaryKeyv2(dkSecretKey.getPackageName(), dkSecretKey.getSha1(),dkSecretKey.getDdVersion()) == null) {
                dkSecretKey.setCreateTime(new Date());
                dkSecretKey.setUpdateTime(new Date());
                if (null != appInfo) {
                    dkSecretKey.setAppid(appInfo.getAppid());
                    dkSecretKey.setAppName(appInfo.getAppName());
                }
                dkSecretKeyService.insertSelectivev2(dkSecretKey);
            } else {
                return ReturnJson.toErrorJson("秘钥已存在");
            }
        }else if("2.0".equals(dkSecretKey.getDdVersion())){
            if (BlankUtils.checkBlank(dkSecretKey.getPackageName()) ) {
                return ReturnJson.error(Constants.ParamError);
            }
        	key=DDK.genDDK(dkSecretKey.getPackageName(), dkSecretKey.getSha1());
        	dkSecretKey.setSecretKey(key);
        	//通过包名查找appid及appName
        	DkSecretKey appInfo=dkSecretKeyService.selectAppInfoByPkg(dkSecretKey.getPackageName());
        	//应用信息不存在进行提示返回
        	if(null==appInfo){
        		return ReturnJson.toErrorJson("未查找到相应的应用信息");
        	}
        	//该秘钥是否存在
            if (dkSecretKeyService.selectByPrimaryKeyv2(dkSecretKey.getPackageName(), dkSecretKey.getSha1(),dkSecretKey.getDdVersion()) == null) {
                dkSecretKey.setCreateTime(new Date());
                dkSecretKey.setUpdateTime(new Date());
                if(null!=appInfo){
                	dkSecretKey.setAppid(appInfo.getAppid());
                	dkSecretKey.setAppName(appInfo.getAppName());
                }
                dkSecretKeyService.insertSelectivev2(dkSecretKey);
            }else{
            	return ReturnJson.toErrorJson("秘钥已存在");
            }
        }else{
            key = DkGenarator.genarator(dkSecretKey.getDdVersion(), dkSecretKey.getSha1());
            dkSecretKey.setSecretKey(key);
            //该秘钥是否存在
            if (dkSecretKeyService.selectByPrimaryKey(dkSecretKey.getDdVersion(), dkSecretKey.getSha1()) == null) {
                dkSecretKey.setCreateTime(new Date());
                dkSecretKey.setUpdateTime(new Date());
                dkSecretKeyService.insertSelective(dkSecretKey);
            }else{
                dkSecretKey.setUpdateTime(new Date());
                dkSecretKeyService.updateByPrimaryKeySelective(dkSecretKey);
            }
        }
        // 返回生成的密钥内容 -张炳杰.20230316
        JSONObject info = new JSONObject();
        info.put("secretKey", key);
        return ReturnJson.success(info);

    }


    /**
     * 得到dk版本
     *
     * @return
     */
    @RequestMapping(value = "/getVersions")
    public String getVersions() {
        List<String> versions = DkUtils.getVersions();
        return ReturnJson.success(versions);

    }


    @PostMapping(value = "/getSha1")
    public String selectSha1() {

        return ReturnJson.success(dkSecretKeyService.selectSha1());
    }

    /**
     * dk密钥删除功能，根据dd版本和sha1，来删除该条记录
     * @param primaryKeyStr
     * @return
     */
    @RequestMapping(value = "delete")
    public String deleteByPrimaryKey(String primaryKeyStr) {
        String errorInfo = "{\"ret\":0,\"msg\":\"参数为空!\"}";
        if(BlankUtils.checkBlank(primaryKeyStr)){
            return errorInfo;
        }

        String[] argsArr = primaryKeyStr.split(",");
        String ddVersion = argsArr[0];
        String sha1 = argsArr[1];

        if(BlankUtils.checkBlank(ddVersion) || BlankUtils.checkBlank(sha1)){
            return errorInfo;
        }
        dkSecretKeyService.deleteByPrimaryKey(ddVersion, sha1);
        return "{\"ret\":1,\"msg\":\"删除成功!\"}";
    }
}
