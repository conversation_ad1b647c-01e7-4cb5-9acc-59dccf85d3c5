# 华为小游戏数据适配任务

## 任务背景
在 `syncMonetizationReport` 函数中增加"华为小游戏数据"进行适配，将华为小游戏的数据以准确的结构写入到 `dn_report_monetization_summary_china` 表中。

## 执行计划
1. **扩展现有的小游戏数据处理逻辑**
   - 在 `syncMonetizationReport` 函数中增加华为快游戏-应用(57)的支持
   - 创建专门的华为小游戏数据同步函数

2. **数据源分析**
   - 华为快游戏-游戏：app_category = 45
   - 华为快游戏-应用：app_category = 57
   - 数据来源：`dnwx_bi.ads_user_iap_revenue_info_daily` 表

3. **字段映射**
   - `tdate` -> `day`
   - `appid` -> `app`
   - `CONCAT('hw_quickgame_',download_channel)` -> `cha_id`
   - `add_user_cnt` -> `installs`
   - `active_user_cnt` -> `dau`
   - `(add_user_iap_revenue + old_user_iap_revenue)/100` -> `revenue`

## 已完成的修改

### 1. 修改 Adv2ServiceImpl.java
**文件路径**: `src/main/java/com/wbgame/service/adv2/impl/Adv2ServiceImpl.java`

#### 修改1: 扩展小游戏类型支持
- **位置**: 第1023行
- **修改前**: `app_category in (17,39,43,47,45)`
- **修改后**: `app_category in (17,39,43,47,45,57)`
- **说明**: 增加华为快游戏-应用(57)的支持

#### 修改2: 增加华为小游戏专门处理
- **位置**: 第1037行
- **新增**: `syncHuaweiQuickGameMonetizationData(paramMap);`
- **说明**: 调用专门的华为小游戏数据同步函数

#### 修改3: 新增华为小游戏数据同步函数
- **位置**: 第2384-2459行
- **函数名**: `syncHuaweiQuickGameMonetizationData`
- **功能**: 
  - 查询华为小游戏数据（app_category 45和57）
  - 构建变现数据结构
  - 批量写入 `dn_report_monetization_summary_china` 表
  - 使用 `hw_quickgame_` 前缀标识华为小游戏渠道

## 数据处理逻辑

### 华为小游戏数据查询
```sql
select tdate,appid,CONCAT('hw_quickgame_',download_channel) cha_id,
       add_user_cnt add_num,active_user_cnt act_num,
       add_user_iap_revenue/100 as add_revenue,
       old_user_iap_revenue/100 as old_revenue,
       (add_user_iap_revenue + old_user_iap_revenue)/100 as total_revenue 
from dnwx_bi.ads_user_iap_revenue_info_daily 
where tdate BETWEEN #{obj.start_date} AND #{obj.end_date} 
and appid in (select id from dnwx_bi.app_info where app_category in (45,57))
```

### 渠道信息设置
- `cha_type_name`: "华为小游戏"
- `cha_media`: "华为"
- `cha_sub_launch`: "华为快游戏"
- `country`: "CN"

### 数据去重策略
- 先删除当天的华为小游戏数据（`cha_id like 'hw_quickgame_%'`）
- 再批量插入新数据，避免重复

## 测试验证
1. 确保华为小游戏数据能正确查询
2. 验证数据格式和字段映射正确性
3. 检查数据写入 `dn_report_monetization_summary_china` 表的完整性
4. 验证渠道ID前缀 `hw_quickgame_` 的正确性

## 注意事项
1. 华为小游戏数据使用独立的渠道ID前缀 `hw_quickgame_`，与其他小游戏区分
2. 收入数据需要除以100进行单位转换（分转元）
3. 批量处理时按10000条分批，避免内存溢出
4. 异常处理确保不影响主流程执行

## 相关文件
- `src/main/java/com/wbgame/service/adv2/impl/Adv2ServiceImpl.java`
- `src/main/java/com/wbgame/pojo/adv2/reportEntity/ChinaMonetizationReport.java`
- `src/main/java/com/wbgame/mapper/adv2/MonetizationSummaryMapper.java`
